"use client"

import { useState, useEffect } from 'react'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { Navigation } from '@/components/Navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { UserForm } from '@/components/admin/UserForm'
import { EditUserDialog } from '@/components/admin/EditUserDialog'
import { ConfirmDialog } from '@/components/ui/confirm-dialog'
import { ToastContainer, useToast } from '@/components/ui/toast'
import Link from 'next/link'
import { 
  Users, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  UserCheck,
  UserX,
  Shield,
  GraduationCap,
  School,
  Truck,
  ArrowLeft,
  Download,
  Upload,
  RefreshCw
} from 'lucide-react'

// أنواع البيانات
interface User {
  id: string
  email: string
  full_name: string
  phone?: string
  role: 'admin' | 'student' | 'school' | 'delivery'
  status: 'active' | 'inactive' | 'suspended'
  created_at: string
  last_login?: string
  avatar_url?: string
  school_name?: string
  delivery_company?: string
  student_id?: string
  verified: boolean
}

const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    full_name: 'مدير النظام',
    phone: '+971501234567',
    role: 'admin',
    status: 'active',
    created_at: '2024-01-15T10:00:00Z',
    last_login: '2024-01-20T14:30:00Z',
    verified: true
  },
  {
    id: '2',
    email: '<EMAIL>',
    full_name: 'أحمد محمد علي',
    phone: '+971507654321',
    role: 'student',
    status: 'active',
    created_at: '2024-01-16T09:15:00Z',
    last_login: '2024-01-20T12:45:00Z',
    student_id: 'STU2024001',
    verified: true
  },
  {
    id: '3',
    email: '<EMAIL>',
    full_name: 'جامعة الإمارات',
    phone: '+97126123456',
    role: 'school',
    status: 'active',
    created_at: '2024-01-10T08:00:00Z',
    last_login: '2024-01-20T11:20:00Z',
    school_name: 'جامعة الإمارات العربية المتحدة',
    verified: true
  },
  {
    id: '4',
    email: '<EMAIL>',
    full_name: 'شركة التوصيل السريع',
    phone: '+97144567890',
    role: 'delivery',
    status: 'active',
    created_at: '2024-01-12T07:30:00Z',
    last_login: '2024-01-19T16:10:00Z',
    delivery_company: 'شركة التوصيل السريع',
    verified: false
  }
]

export default function UsersManagementPage() {
  const [users, setUsers] = useState<User[]>(mockUsers)
  const [filteredUsers, setFilteredUsers] = useState<User[]>(mockUsers)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [showUserForm, setShowUserForm] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [userToDelete, setUserToDelete] = useState<User | null>(null)
  const [loading, setLoading] = useState(false)
  const { showToast } = useToast()

  // تصفية المستخدمين
  useEffect(() => {
    let filtered = users

    // تصفية بالبحث
    if (searchTerm) {
      filtered = filtered.filter(user => 
        user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.phone?.includes(searchTerm) ||
        user.student_id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.school_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.delivery_company?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // تصفية بالدور
    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.role === roleFilter)
    }

    // تصفية بالحالة
    if (statusFilter !== 'all') {
      filtered = filtered.filter(user => user.status === statusFilter)
    }

    setFilteredUsers(filtered)
  }, [users, searchTerm, roleFilter, statusFilter])

  // إضافة مستخدم جديد
  const handleAddUser = (userData: Partial<User>) => {
    const newUser: User = {
      id: Date.now().toString(),
      email: userData.email!,
      full_name: userData.full_name!,
      phone: userData.phone,
      role: userData.role!,
      status: 'active',
      created_at: new Date().toISOString(),
      verified: false,
      ...userData
    }

    setUsers(prev => [newUser, ...prev])
    setShowUserForm(false)
    showToast('تم إضافة المستخدم بنجاح', 'success')
  }

  // تعديل مستخدم
  const handleEditUser = (userData: Partial<User>) => {
    if (!editingUser) return

    setUsers(prev => prev.map(user => 
      user.id === editingUser.id 
        ? { ...user, ...userData }
        : user
    ))
    setEditingUser(null)
    showToast('تم تحديث بيانات المستخدم بنجاح', 'success')
  }

  // حذف مستخدم
  const handleDeleteUser = () => {
    if (!userToDelete) return

    setUsers(prev => prev.filter(user => user.id !== userToDelete.id))
    setUserToDelete(null)
    showToast('تم حذف المستخدم بنجاح', 'success')
  }

  // تغيير حالة المستخدم
  const handleToggleUserStatus = (userId: string) => {
    setUsers(prev => prev.map(user => 
      user.id === userId 
        ? { 
            ...user, 
            status: user.status === 'active' ? 'suspended' : 'active' 
          }
        : user
    ))
    showToast('تم تحديث حالة المستخدم', 'success')
  }

  // تحديث حالة التحقق
  const handleToggleVerification = (userId: string) => {
    setUsers(prev => prev.map(user => 
      user.id === userId 
        ? { ...user, verified: !user.verified }
        : user
    ))
    showToast('تم تحديث حالة التحقق', 'success')
  }

  // الحصول على أيقونة الدور
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return <Shield className="h-4 w-4" />
      case 'student': return <GraduationCap className="h-4 w-4" />
      case 'school': return <School className="h-4 w-4" />
      case 'delivery': return <Truck className="h-4 w-4" />
      default: return <Users className="h-4 w-4" />
    }
  }

  // الحصول على لون الحالة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'inactive': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
      case 'suspended': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // الحصول على نص الحالة
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط'
      case 'inactive': return 'غير نشط'
      case 'suspended': return 'معلق'
      default: return status
    }
  }

  // الحصول على نص الدور
  const getRoleText = (role: string) => {
    switch (role) {
      case 'admin': return 'مدير'
      case 'student': return 'طالب'
      case 'school': return 'مدرسة'
      case 'delivery': return 'توصيل'
      default: return role
    }
  }

  return (
    <ProtectedRoute allowedRoles={['admin']}>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        
        <main className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-4 mb-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/dashboard/admin">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  العودة للوحة التحكم
                </Link>
              </Button>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                  إدارة المستخدمين 👥
                </h1>
                <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                  إدارة حسابات المستخدمين والصلاحيات والأدوار
                </p>
              </div>
              <Button onClick={() => setShowUserForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                إضافة مستخدم
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="product-grid grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium arabic-text">إجمالي المستخدمين</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="number text-2xl font-bold">{users.length}</div>
                <p className="text-xs text-muted-foreground arabic-text">
                  +{users.filter(u => u.status === 'active').length} نشط
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium arabic-text">الطلاب</CardTitle>
                <GraduationCap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="number text-2xl font-bold">
                  {users.filter(u => u.role === 'student').length}
                </div>
                <p className="text-xs text-muted-foreground arabic-text">
                  مستخدم طالب
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium arabic-text">المدارس</CardTitle>
                <School className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="number text-2xl font-bold">
                  {users.filter(u => u.role === 'school').length}
                </div>
                <p className="text-xs text-muted-foreground arabic-text">
                  مؤسسة تعليمية
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium arabic-text">شركات التوصيل</CardTitle>
                <Truck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="number text-2xl font-bold">
                  {users.filter(u => u.role === 'delivery').length}
                </div>
                <p className="text-xs text-muted-foreground arabic-text">
                  شركة توصيل
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Filters and Search */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="arabic-text">البحث والتصفية</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="البحث بالاسم، البريد الإلكتروني، الهاتف..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 arabic-text"
                    />
                  </div>
                </div>
                
                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="تصفية بالدور" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأدوار</SelectItem>
                    <SelectItem value="admin">مدير</SelectItem>
                    <SelectItem value="student">طالب</SelectItem>
                    <SelectItem value="school">مدرسة</SelectItem>
                    <SelectItem value="delivery">توصيل</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="تصفية بالحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الحالات</SelectItem>
                    <SelectItem value="active">نشط</SelectItem>
                    <SelectItem value="inactive">غير نشط</SelectItem>
                    <SelectItem value="suspended">معلق</SelectItem>
                  </SelectContent>
                </Select>

                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    تصدير
                  </Button>
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    استيراد
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Users Table */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="arabic-text">
                  قائمة المستخدمين ({filteredUsers.length})
                </CardTitle>
                <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  تحديث
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right py-3 px-4 font-medium arabic-text">المستخدم</th>
                      <th className="text-right py-3 px-4 font-medium arabic-text">الدور</th>
                      <th className="text-right py-3 px-4 font-medium arabic-text">الحالة</th>
                      <th className="text-right py-3 px-4 font-medium arabic-text">التحقق</th>
                      <th className="text-right py-3 px-4 font-medium arabic-text">آخر دخول</th>
                      <th className="text-right py-3 px-4 font-medium arabic-text">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredUsers.map((user) => (
                      <tr key={user.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                              <span className="text-blue-600 dark:text-blue-400 font-medium">
                                {user.full_name.charAt(0)}
                              </span>
                            </div>
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white arabic-text">
                                {user.full_name}
                              </p>
                              <p className="text-sm text-gray-500">{user.email}</p>
                              {user.phone && (
                                <p className="text-xs text-gray-400">{user.phone}</p>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-2">
                            {getRoleIcon(user.role)}
                            <span className="arabic-text">{getRoleText(user.role)}</span>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <Badge className={getStatusColor(user.status)}>
                            {getStatusText(user.status)}
                          </Badge>
                        </td>
                        <td className="py-4 px-4">
                          {user.verified ? (
                            <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                              محقق
                            </Badge>
                          ) : (
                            <Badge variant="outline">
                              غير محقق
                            </Badge>
                          )}
                        </td>
                        <td className="py-4 px-4">
                          <span className="text-sm text-gray-500">
                            {user.last_login 
                              ? new Date(user.last_login).toLocaleDateString('ar-AE')
                              : 'لم يدخل بعد'
                            }
                          </span>
                        </td>
                        <td className="py-4 px-4">
                          <div className="actions flex items-center gap-2">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel className="arabic-text">الإجراءات</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => setEditingUser(user)}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  تعديل
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleToggleUserStatus(user.id)}>
                                  {user.status === 'active' ? (
                                    <>
                                      <UserX className="h-4 w-4 mr-2" />
                                      تعليق
                                    </>
                                  ) : (
                                    <>
                                      <UserCheck className="h-4 w-4 mr-2" />
                                      تفعيل
                                    </>
                                  )}
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleToggleVerification(user.id)}>
                                  {user.verified ? 'إلغاء التحقق' : 'تحقق'}
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  onClick={() => setUserToDelete(user)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  حذف
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {filteredUsers.length === 0 && (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 arabic-text">لا توجد مستخدمين</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </main>

        {/* User Form Dialog */}
        {showUserForm && (
          <UserForm
            onSubmit={handleAddUser}
            onCancel={() => setShowUserForm(false)}
          />
        )}

        {/* Edit User Dialog */}
        {editingUser && (
          <EditUserDialog
            user={editingUser}
            onSubmit={handleEditUser}
            onCancel={() => setEditingUser(null)}
          />
        )}

        {/* Delete Confirmation */}
        {userToDelete && (
          <ConfirmDialog
            title="حذف المستخدم"
            message={`هل أنت متأكد من حذف المستخدم "${userToDelete.full_name}"؟ هذا الإجراء لا يمكن التراجع عنه.`}
            onConfirm={handleDeleteUser}
            onCancel={() => setUserToDelete(null)}
            confirmText="حذف"
            cancelText="إلغاء"
            variant="destructive"
          />
        )}

        <ToastContainer />
      </div>
    </ProtectedRoute>
  )
}
